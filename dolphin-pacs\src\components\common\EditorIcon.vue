<template>
  <span
    class="editor-icon"
    :style="{
      width: size + 'px',
      height: size + 'px',
      color: color
    }"
    v-html="iconSvg"
  />
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  name: string
  size?: number
  color?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 16,
  color: 'currentColor'
})

// 预定义的图标内容（从 SVG 文件中提取的 path 内容）
const iconContents: Record<string, string> = {
  // 文本格式
  'bold': `<svg viewBox="0 0 1024 1024" fill="currentColor"><path d="M694.021847 492.679245a208.460775 208.460775 0 0 1 112.873883 74.740815 197.275074 197.275074 0 0 1 37.624627 118.975173 234.899702 234.899702 0 0 1-34.065541 123.551142A203.376365 203.376365 0 0 1 711.817279 892.313803a440.309831 440.309831 0 0 1-168.802383 26.438928h-254.220457a50.844091 50.844091 0 0 1-50.844091-50.844091V168.802383a50.844091 50.844091 0 0 1 50.844091-50.844091h249.644489q145.922542 0 203.376365 63.555114a203.376365 203.376365 0 0 1 59.996028 140.838133 180.496524 180.496524 0 0 1-27.45581 97.112215 206.935452 206.935452 0 0 1-80.333664 73.215491z m-323.368421-39.14995h147.956306A563.860973 563.860973 0 0 0 610.129096 447.428004a122.53426 122.53426 0 0 0 64.063555-31.523337 101.688183 101.688183 0 0 0 27.964251-79.825223A111.34856 111.34856 0 0 0 672.667329 254.220457a121.008937 121.008937 0 0 0-67.114201-33.04866 681.310824 681.310824 0 0 0-101.688183-6.101291H370.653426a25.422046 25.422046 0 0 0-25.422046 25.422046v187.614697a25.422046 25.422046 0 0 0 25.422046 25.422046z m0 370.144985h173.886792a227.781529 227.781529 0 0 0 148.464747-36.607746 127.618669 127.618669 0 0 0 41.692155-101.688182 124.059583 124.059583 0 0 0-44.234359-101.688183 259.304866 259.304866 0 0 0-159.650447-36.099305H370.653426a25.422046 25.422046 0 0 0-25.422046 25.422046v225.239324a25.422046 25.422046 0 0 0 25.422046 25.422046z"/></svg>`,

  'italic': `<svg viewBox="0 0 1024 1024" fill="currentColor"><path d="M768 85.792h-288a32 32 0 0 0 0 64h96.32l-230.336 704H256a32 32 0 0 0 0 64h288a32 32 0 0 0 0-64h-93.728l230.528-704H768a32 32 0 0 0 0-64z"/></svg>`,

  'strikethrough': `<svg viewBox="0 0 1024 1024" fill="currentColor"><path d="M667.52 257.92H293.12v140.8h82.56v-58.24h104.96v343.04h-61.44v82.56h205.44v-82.56H563.2V340.48h104.32v58.24h82.56v-140.8h-82.56z"/><path d="M283.52 480h456.96v64H283.52z"/></svg>`,

  // 其他图标将在下一个编辑中添加
}

// 动态获取图标
const iconSvg = computed(() => {
  const content = iconContents[props.name]
  if (!content) {
    console.warn(`图标 "${props.name}" 未找到`)
    return ''
  }
  return content
})
</script>

<style lang="scss" scoped>
.editor-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  
  :deep(svg) {
    width: 100%;
    height: 100%;
    fill: currentColor;
  }
}
</style>
