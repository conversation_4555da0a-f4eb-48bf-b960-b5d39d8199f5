<script lang="ts" setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch } from 'vue'
import { useEditor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import { Table } from '@tiptap/extension-table'
import { TableRow } from '@tiptap/extension-table-row'
import { TableHeader } from '@tiptap/extension-table-header'
import { TableCell } from '@tiptap/extension-table-cell'
import Image from '@tiptap/extension-image'
import TextAlign from '@tiptap/extension-text-align'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'TemplateEditor'
})

interface Props {
  modelValue?: string
  placeholder?: string
  editable?: boolean
  height?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入内容...',
  editable: true,
  height: '500px'
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'save': [content: string]
  'change': [content: string]
}>()

// 编辑器实例
const editor = useEditor({
  content: props.modelValue,
  editable: props.editable,
  extensions: [
    StarterKit.configure({
      heading: {
        levels: [1, 2, 3, 4, 5, 6]
      }
    }),
    Table.configure({
      resizable: true,
      handleWidth: 5,
      cellMinWidth: 50
    }),
    TableRow,
    TableHeader,
    TableCell,
    Image.configure({
      inline: true,
      allowBase64: true,
      HTMLAttributes: {
        class: 'editor-image',
      },
    }),
    TextAlign.configure({
      types: ['heading', 'paragraph'],
      alignments: ['left', 'center', 'right', 'justify'],
      defaultAlignment: 'left',
    })
  ],
  onUpdate: ({ editor }) => {
    const html = editor.getHTML()
    emit('update:modelValue', html)
    emit('change', html)
  },
  editorProps: {
    attributes: {
      class: 'tiptap-editor-content'
    }
  }
})

// 工具栏状态
const toolbarState = reactive({
  isBold: false,
  isItalic: false,
  isUnderline: false,
  isStrike: false,
  isCode: false,
  isBulletList: false,
  isOrderedList: false,
  isBlockquote: false,
  currentHeading: 0,
  textAlign: 'left'
})

// 更新工具栏状态
const updateToolbarState = () => {
  if (!editor.value) return

  toolbarState.isBold = editor.value.isActive('bold')
  toolbarState.isItalic = editor.value.isActive('italic')
  toolbarState.isStrike = editor.value.isActive('strike')
  toolbarState.isCode = editor.value.isActive('code')
  toolbarState.isBulletList = editor.value.isActive('bulletList')
  toolbarState.isOrderedList = editor.value.isActive('orderedList')
  toolbarState.isBlockquote = editor.value.isActive('blockquote')

  // 检查当前标题级别
  for (let level = 1; level <= 6; level++) {
    if (editor.value.isActive('heading', { level })) {
      toolbarState.currentHeading = level
      break
    } else {
      toolbarState.currentHeading = 0
    }
  }

  // 检查文本对齐状态
  if (editor.value.isActive({ textAlign: 'left' })) {
    toolbarState.textAlign = 'left'
  } else if (editor.value.isActive({ textAlign: 'center' })) {
    toolbarState.textAlign = 'center'
  } else if (editor.value.isActive({ textAlign: 'right' })) {
    toolbarState.textAlign = 'right'
  } else if (editor.value.isActive({ textAlign: 'justify' })) {
    toolbarState.textAlign = 'justify'
  } else {
    toolbarState.textAlign = 'left'
  }
}

// 监听编辑器选择变化
watch(() => editor.value?.state.selection, () => {
  updateToolbarState()
}, { deep: true })

// 工具栏操作方法
const toggleBold = () => editor.value?.chain().focus().toggleBold().run()
const toggleItalic = () => editor.value?.chain().focus().toggleItalic().run()
const toggleStrike = () => editor.value?.chain().focus().toggleStrike().run()
const toggleCode = () => editor.value?.chain().focus().toggleCode().run()
const toggleBulletList = () => editor.value?.chain().focus().toggleBulletList().run()
const toggleOrderedList = () => editor.value?.chain().focus().toggleOrderedList().run()
const toggleBlockquote = () => editor.value?.chain().focus().toggleBlockquote().run()

const setHeading = (level: number) => {
  if (level === 0) {
    editor.value?.chain().focus().setParagraph().run()
  } else {
    // 确保level是有效的标题级别 (1-6)
    const validLevel = Math.max(1, Math.min(6, level)) as 1 | 2 | 3 | 4 | 5 | 6
    editor.value?.chain().focus().toggleHeading({ level: validLevel }).run()
  }
}

const setTextAlign = (alignment: string) => {
  toolbarState.textAlign = alignment
  editor.value?.chain().focus().setTextAlign(alignment).run()
}

const undo = () => editor.value?.chain().focus().undo().run()
const redo = () => editor.value?.chain().focus().redo().run()

const addHorizontalRule = () => editor.value?.chain().focus().setHorizontalRule().run()

// 表格操作
const insertTable = () => {
  editor.value?.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
}

const addColumnBefore = () => editor.value?.chain().focus().addColumnBefore().run()
const addColumnAfter = () => editor.value?.chain().focus().addColumnAfter().run()
const deleteColumn = () => editor.value?.chain().focus().deleteColumn().run()
const addRowBefore = () => editor.value?.chain().focus().addRowBefore().run()
const addRowAfter = () => editor.value?.chain().focus().addRowAfter().run()
const deleteRow = () => editor.value?.chain().focus().deleteRow().run()
const deleteTable = () => editor.value?.chain().focus().deleteTable().run()
const mergeCells = () => editor.value?.chain().focus().mergeCells().run()
const splitCell = () => editor.value?.chain().focus().splitCell().run()

// 表格命令处理
const handleTableCommand = (command: string) => {
  switch (command) {
    case 'insert':
      insertTable()
      break
    case 'addColumnBefore':
      addColumnBefore()
      break
    case 'addColumnAfter':
      addColumnAfter()
      break
    case 'deleteColumn':
      deleteColumn()
      break
    case 'addRowBefore':
      addRowBefore()
      break
    case 'addRowAfter':
      addRowAfter()
      break
    case 'deleteRow':
      deleteRow()
      break
    case 'mergeCells':
      mergeCells()
      break
    case 'splitCell':
      splitCell()
      break
    case 'deleteTable':
      deleteTable()
      break
  }
}

// 链接操作
const addLink = () => {
  const url = window.prompt('请输入链接地址:')
  if (url) {
    editor.value?.chain().focus().setLink({ href: url }).run()
  }
}

const removeLink = () => editor.value?.chain().focus().unsetLink().run()

// 图片操作
const addImage = () => {
  const url = window.prompt('请输入图片地址:')
  if (url) {
    // 使用TipTap Image扩展的正确命令
    editor.value?.chain().focus().setImage({ src: url, alt: '插入的图片' }).run()
  }
}

// 保存操作
const handleSave = () => {
  const content = editor.value?.getHTML() || ''
  emit('save', content)
  ElMessage.success('保存成功')
}

// 导出操作
const exportHTML = () => {
  const content = editor.value?.getHTML() || ''
  const blob = new Blob([content], { type: 'text/html' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'template.html'
  a.click()
  URL.revokeObjectURL(url)
}

// 导入操作
const importHTML = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.html'
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const content = e.target?.result as string
        editor.value?.commands.setContent(content)
        ElMessage.success('导入成功')
      }
      reader.readAsText(file)
    }
  }
  input.click()
}

// 监听 props 变化
watch(() => props.modelValue, (newValue) => {
  if (editor.value && newValue !== editor.value.getHTML()) {
    editor.value.commands.setContent(newValue)
  }
})

watch(() => props.editable, (newValue) => {
  if (editor.value) {
    editor.value.setEditable(newValue)
  }
})

onMounted(() => {
  updateToolbarState()
})

onBeforeUnmount(() => {
  editor.value?.destroy()
})
</script>

<template>
  <div class="template-editor">
    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <!-- 文件操作 -->
      <div class="toolbar-group">
        <el-button size="small" @click="handleSave" title="保存">
          💾 保存
        </el-button>
        <el-button size="small" @click="exportHTML" title="导出">
          📥 导出
        </el-button>
        <el-button size="small" @click="importHTML" title="导入">
          📤 导入
        </el-button>
      </div>

      <el-divider direction="vertical" />

      <!-- 撤销重做 -->
      <div class="toolbar-group">
        <el-button
          size="small"
          @click="undo"
          :disabled="!editor?.can().undo()"
          title="撤销"
        >
          ↶
        </el-button>
        <el-button
          size="small"
          @click="redo"
          :disabled="!editor?.can().redo()"
          title="重做"
        >
          ↷
        </el-button>
      </div>

      <el-divider direction="vertical" />

      <!-- 标题选择 -->
      <div class="toolbar-group">
        <el-select
          :model-value="toolbarState.currentHeading"
          @change="setHeading"
          size="small"
          style="width: 100px"
          placeholder="标题"
        >
          <el-option :value="0" label="正文" />
          <el-option :value="1" label="标题1" />
          <el-option :value="2" label="标题2" />
          <el-option :value="3" label="标题3" />
          <el-option :value="4" label="标题4" />
          <el-option :value="5" label="标题5" />
          <el-option :value="6" label="标题6" />
        </el-select>
      </div>

      <el-divider direction="vertical" />

      <!-- 文本格式 -->
      <div class="toolbar-group">
        <el-button
          size="small"
          @click="toggleBold"
          :type="toolbarState.isBold ? 'primary' : 'default'"
          title="粗体"
        >
          <strong>B</strong>
        </el-button>
        <el-button
          size="small"
          @click="toggleItalic"
          :type="toolbarState.isItalic ? 'primary' : 'default'"
          title="斜体"
        >
          <em>I</em>
        </el-button>
        <el-button
          size="small"
          @click="toggleStrike"
          :type="toolbarState.isStrike ? 'primary' : 'default'"
          title="删除线"
        >
          <s>S</s>
        </el-button>
        <el-button
          size="small"
          @click="toggleCode"
          :type="toolbarState.isCode ? 'primary' : 'default'"
          title="代码"
        >
          &lt;/&gt;
        </el-button>
      </div>

      <el-divider direction="vertical" />

      <!-- 文本对齐 -->
      <div class="toolbar-group">
        <el-button
          size="small"
          @click="setTextAlign('left')"
          :type="toolbarState.textAlign === 'left' ? 'primary' : 'default'"
          title="左对齐"
        >
          ⬅️
        </el-button>
        <el-button
          size="small"
          @click="setTextAlign('center')"
          :type="toolbarState.textAlign === 'center' ? 'primary' : 'default'"
          title="居中对齐"
        >
          ↔️
        </el-button>
        <el-button
          size="small"
          @click="setTextAlign('right')"
          :type="toolbarState.textAlign === 'right' ? 'primary' : 'default'"
          title="右对齐"
        >
          ➡️
        </el-button>
        <el-button
          size="small"
          @click="setTextAlign('justify')"
          :type="toolbarState.textAlign === 'justify' ? 'primary' : 'default'"
          title="两端对齐"
        >
          ↕️
        </el-button>
      </div>

      <el-divider direction="vertical" />

      <!-- 列表和引用 -->
      <div class="toolbar-group">
        <el-button
          size="small"
          @click="toggleBulletList"
          :type="toolbarState.isBulletList ? 'primary' : 'default'"
          title="无序列表"
        >
          • 列表
        </el-button>
        <el-button
          size="small"
          @click="toggleOrderedList"
          :type="toolbarState.isOrderedList ? 'primary' : 'default'"
          title="有序列表"
        >
          1. 列表
        </el-button>
        <el-button
          size="small"
          @click="toggleBlockquote"
          :type="toolbarState.isBlockquote ? 'primary' : 'default'"
          title="引用"
        >
          " 引用
        </el-button>
      </div>

      <el-divider direction="vertical" />

      <!-- 表格操作 -->
      <div class="toolbar-group">
        <el-dropdown @command="handleTableCommand">
          <el-button size="small" title="表格">
            📊 表格 ▼
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="insert">插入表格</el-dropdown-item>
              <el-dropdown-item command="addColumnBefore">在前面插入列</el-dropdown-item>
              <el-dropdown-item command="addColumnAfter">在后面插入列</el-dropdown-item>
              <el-dropdown-item command="deleteColumn">删除列</el-dropdown-item>
              <el-dropdown-item command="addRowBefore">在上面插入行</el-dropdown-item>
              <el-dropdown-item command="addRowAfter">在下面插入行</el-dropdown-item>
              <el-dropdown-item command="deleteRow">删除行</el-dropdown-item>
              <el-dropdown-item command="mergeCells">合并单元格</el-dropdown-item>
              <el-dropdown-item command="splitCell">拆分单元格</el-dropdown-item>
              <el-dropdown-item command="deleteTable" divided>删除表格</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <el-divider direction="vertical" />

      <!-- 插入操作 -->
      <div class="toolbar-group">
        <el-button size="small" @click="addLink" title="插入链接">
          🔗 链接
        </el-button>
        <el-button size="small" @click="addImage" title="插入图片">
          🖼️ 图片
        </el-button>
        <el-button size="small" @click="addHorizontalRule" title="分割线">
          ➖ 分割线
        </el-button>
      </div>
    </div>

    <!-- 编辑器内容区域 -->
    <div class="editor-content-wrapper" :style="{ height: typeof props.height === 'number' ? `${props.height}px` : props.height }">
      <EditorContent
        :editor="editor"
        class="editor-content"
        :placeholder="props.placeholder"
      />
    </div>

    <!-- 状态栏 -->
    <div class="editor-status-bar">
      <div class="status-left">
        <span class="word-count">
          字符数: {{ editor?.storage.characterCount?.characters() || 0 }}
        </span>
        <span class="word-count">
          单词数: {{ editor?.storage.characterCount?.words() || 0 }}
        </span>
      </div>
      <div class="status-right">
        <span class="editor-mode">
          {{ props.editable ? '📝 编辑模式' : '👁️ 只读模式' }}
        </span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.template-editor {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
  background: white;

  .editor-toolbar {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: var(--el-bg-color-page);
    border-bottom: 1px solid var(--el-border-color);
    flex-wrap: wrap;
    gap: 8px;

    .toolbar-group {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .el-divider--vertical {
      height: 20px;
      margin: 0 8px;
    }

    // 自定义按钮样式，确保图标清晰可见
    .el-button {
      min-width: auto;
      
      strong, em, s {
        font-style: normal;
        font-weight: bold;
      }
      
      em {
        font-style: italic;
        font-weight: normal;
      }
    }
  }

  .editor-content-wrapper {
    position: relative;
    overflow-y: auto;

    .editor-content {
      height: 100%;

      :deep(.tiptap-editor-content) {
        padding: 20px;
        outline: none;
        min-height: 100%;

        // 基础样式
        p {
          margin: 0 0 16px 0;
          line-height: 1.6;

          &:last-child {
            margin-bottom: 0;
          }
        }

        // 标题样式
        h1, h2, h3, h4, h5, h6 {
          margin: 24px 0 16px 0;
          font-weight: 600;
          line-height: 1.4;

          &:first-child {
            margin-top: 0;
          }
        }

        h1 { font-size: 2em; }
        h2 { font-size: 1.5em; }
        h3 { font-size: 1.25em; }
        h4 { font-size: 1.1em; }
        h5 { font-size: 1em; }
        h6 { font-size: 0.9em; }

        // 列表样式
        ul, ol {
          margin: 16px 0;
          padding-left: 24px;

          li {
            margin: 4px 0;
            line-height: 1.6;
          }
        }

        // 引用样式
        blockquote {
          margin: 16px 0;
          padding: 12px 16px;
          border-left: 4px solid var(--el-color-primary);
          background: var(--el-bg-color-page);
          font-style: italic;

          p {
            margin: 0;
          }
        }

        // 代码样式
        code {
          background: var(--el-bg-color-page);
          padding: 2px 6px;
          border-radius: 4px;
          font-family: 'Courier New', monospace;
          font-size: 0.9em;
        }

        pre {
          background: var(--el-bg-color-page);
          padding: 16px;
          border-radius: 8px;
          overflow-x: auto;
          margin: 16px 0;

          code {
            background: none;
            padding: 0;
          }
        }

        // 分割线样式
        hr {
          margin: 24px 0;
          border: none;
          border-top: 2px solid var(--el-border-color);
        }

        // 链接样式
        a {
          color: var(--el-color-primary);
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }

        // 图片样式
        img {
          max-width: 100%;
          height: auto;
          border-radius: 8px;
          margin: 16px 0;
        }

        // 表格样式
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 16px 0;

          th, td {
            border: 1px solid var(--el-border-color);
            padding: 8px 12px;
            text-align: left;
            vertical-align: top;
          }

          th {
            background: var(--el-bg-color-page);
            font-weight: 600;
          }

          tr:nth-child(even) {
            background: var(--el-bg-color-page);
          }
        }

        // 占位符样式
        .is-empty::before {
          content: attr(data-placeholder);
          float: left;
          color: var(--el-text-color-placeholder);
          pointer-events: none;
          height: 0;
        }
      }
    }
  }

  .editor-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: var(--el-bg-color-page);
    border-top: 1px solid var(--el-border-color);
    font-size: 12px;
    color: var(--el-text-color-secondary);

    .status-left {
      display: flex;
      gap: 16px;
    }

    .word-count {
      font-family: monospace;
    }

    .editor-mode {
      font-weight: 500;
    }
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .template-editor {
    .editor-toolbar {
      padding: 8px 12px;

      .toolbar-group {
        gap: 2px;
      }

      .el-button {
        padding: 6px 8px;
        font-size: 12px;
      }
    }

    .editor-content-wrapper {
      .editor-content {
        :deep(.tiptap-editor-content) {
          padding: 12px;
        }
      }
    }

    .editor-status-bar {
      padding: 6px 12px;
      font-size: 11px;

      .status-left {
        gap: 8px;
      }
    }
  }
}
</style>