<template>
  <div class="case-templates-page">
    <div class="templates-container">
      <div
        v-for="template in templates"
        :key="template.id"
        class="card"
        @click="handleTemplateClick(template)"
      >
        <!-- 背景图片区域 -->
        <div class="card__img">
          <svg xmlns="http://www.w3.org/2000/svg" width="100%">
            <rect fill="#ffffff" width="540" height="450"></rect>
            <defs>
              <linearGradient id="a" gradientUnits="userSpaceOnUse" x1="0" x2="0" y1="0" y2="100%" gradientTransform="rotate(222,648,379)">
                <stop offset="0" stop-color="#ffffff"></stop>
                <stop offset="1" :stop-color="getCategoryColor(template.category)"></stop>
              </linearGradient>
              <pattern patternUnits="userSpaceOnUse" id="b" width="300" height="250" x="0" y="0" viewBox="0 0 1080 900">
                <g fill-opacity="0.5">
                  <polygon fill="#444" points="90 150 0 300 180 300"></polygon>
                  <polygon points="90 150 180 0 0 0"></polygon>
                  <polygon fill="#AAA" points="270 150 360 0 180 0"></polygon>
                  <polygon fill="#DDD" points="450 150 360 300 540 300"></polygon>
                  <polygon fill="#999" points="450 150 540 0 360 0"></polygon>
                  <polygon points="630 150 540 300 720 300"></polygon>
                  <polygon fill="#DDD" points="630 150 720 0 540 0"></polygon>
                  <polygon fill="#444" points="810 150 720 300 900 300"></polygon>
                  <polygon fill="#FFF" points="810 150 900 0 720 0"></polygon>
                  <polygon fill="#DDD" points="990 150 900 300 1080 300"></polygon>
                  <polygon fill="#444" points="990 150 1080 0 900 0"></polygon>
                  <polygon fill="#DDD" points="90 450 0 600 180 600"></polygon>
                  <polygon points="90 450 180 300 0 300"></polygon>
                  <polygon fill="#666" points="270 450 180 600 360 600"></polygon>
                  <polygon fill="#AAA" points="270 450 360 300 180 300"></polygon>
                  <polygon fill="#DDD" points="450 450 360 600 540 600"></polygon>
                  <polygon fill="#999" points="450 450 540 300 360 300"></polygon>
                  <polygon fill="#999" points="630 450 540 600 720 600"></polygon>
                  <polygon fill="#FFF" points="630 450 720 300 540 300"></polygon>
                  <polygon points="810 450 720 600 900 600"></polygon>
                  <polygon fill="#DDD" points="810 450 900 300 720 300"></polygon>
                  <polygon fill="#AAA" points="990 450 900 600 1080 600"></polygon>
                  <polygon fill="#444" points="990 450 1080 300 900 300"></polygon>
                  <polygon fill="#222" points="90 750 0 900 180 900"></polygon>
                  <polygon points="270 750 180 900 360 900"></polygon>
                  <polygon fill="#DDD" points="270 750 360 600 180 600"></polygon>
                  <polygon points="450 750 540 600 360 600"></polygon>
                  <polygon points="630 750 540 900 720 900"></polygon>
                  <polygon fill="#444" points="630 750 720 600 540 600"></polygon>
                  <polygon fill="#AAA" points="810 750 720 900 900 900"></polygon>
                  <polygon fill="#666" points="810 750 900 600 720 600"></polygon>
                  <polygon fill="#999" points="990 750 900 900 1080 900"></polygon>
                </g>
              </pattern>
            </defs>
            <rect x="0" y="0" fill="url(#a)" width="100%" height="100%"></rect>
            <rect x="0" y="0" fill="url(#b)" width="100%" height="100%"></rect>
          </svg>
        </div>

        <!-- 分类标签（作为头像区域） -->
        <div class="card__avatar">
          <div class="category-tag">{{ template.category }}</div>
        </div>

        <!-- 模板标题 -->
        <div class="card__title">{{ template.name }}</div>

        <!-- 模板描述 -->
        <div class="card__subtitle">{{ template.description }}</div>

        <!-- 按钮组 -->
        <div class="card__wrapper">
          <button
            class="card__btn"
            @click.stop="handlePreview(template)"
          >
            review
          </button>
          <button
            class="card__btn card__btn-solid"
            @click.stop="handleUseTemplate(template)"
          >
            use_model
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

defineOptions({
  name: "CaseTemplates"
})

const router = useRouter()

// 根据分类获取颜色
const getCategoryColor = (category: string): string => {
  const colorMap: Record<string, string> = {
    '心血管': '#FC726E',
    '消化系统': '#4BC190',
    '内分泌': '#356CB6',
    '妇产科': '#F85565',
    '血管外科': '#7F3838',
    '骨科': '#BC5B57'
  }
  return colorMap[category] || '#FC726E'
}

// 模板数据接口
interface Template {
  id: string
  name: string
  description: string
  category: string
  downloadUrl?: string
}

// 模拟模板数据
const templates = ref<Template[]>([
  {
    id: '1',
    name: '心脏超声检查报告',
    description: '适用于心脏超声检查的标准报告模板',
    category: '心血管',
    downloadUrl: '/downloads/cardiac-template.docx'
  },
  {
    id: '2',
    name: '腹部超声检查报告',
    description: '适用于腹部器官超声检查的报告模板',
    category: '消化系统',
    downloadUrl: '/downloads/abdominal-template.docx'
  },
  {
    id: '3',
    name: '甲状腺超声检查报告',
    description: '专门用于甲状腺超声检查的报告模板',
    category: '内分泌',
    downloadUrl: '/downloads/thyroid-template.docx'
  },
  {
    id: '4',
    name: '产科超声检查报告',
    description: '适用于孕期超声检查的专业报告模板',
    category: '妇产科',
    downloadUrl: '/downloads/obstetric-template.docx'
  },
  {
    id: '5',
    name: '血管超声检查报告',
    description: '用于血管系统超声检查的报告模板',
    category: '血管外科',
    downloadUrl: '/downloads/vascular-template.docx'
  },
  {
    id: '6',
    name: '肌骨超声检查报告',
    description: '适用于肌肉骨骼系统超声检查的模板',
    category: '骨科',
    downloadUrl: '/downloads/musculoskeletal-template.docx'
  }
])

// 点击卡片进入编辑器
const handleTemplateClick = (template: Template) => {
  router.push({
    name: 'TemplateEditor',
    params: { id: template.id },
    query: { name: template.name }
  })
}

// 预览模板
const handlePreview = (template: Template) => {
  ElMessage.info(`预览模板：${template.name}`)
  // TODO: 实现预览功能，可能是弹窗或跳转到预览页面
  console.log('预览模板:', template)
}

// 使用模板
const handleUseTemplate = (template: Template) => {
  router.push({
    name: 'TemplateEditor',
    params: { id: template.id },
    query: { name: template.name }
  })
}
</script>

<style lang="scss" scoped>
.case-templates-page {
  padding: 24px;
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
}

.templates-container {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  justify-content: center;
  max-width: 1400px;
  margin: 0 auto;
}

.card {
  --main-color: #000;
  --submain-color: #78858F;
  --bg-color: #fff;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  position: relative;
  width: 300px;
  height: 384px;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 20px;
  background: var(--bg-color);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
  }
}

.card__img {
  height: 192px;
  width: 100%;

  svg {
    height: 100%;
    border-radius: 20px 20px 0 0;
  }
}

.card__avatar {
  position: absolute;
  width: 114px;
  height: 114px;
  background: var(--bg-color);
  border-radius: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  top: calc(50% - 57px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

  .category-tag {
    background: var(--el-color-primary);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
  }
}

.card__title {
  margin-top: 60px;
  font-weight: 600;
  font-size: 18px;
  color: var(--main-color);
  text-align: center;
  padding: 0 20px;
  line-height: 1.4;
}

.card__subtitle {
  margin-top: 10px;
  font-weight: 400;
  font-size: 14px;
  color: var(--submain-color);
  text-align: center;
  padding: 0 20px;
  line-height: 1.5;
}

.card__wrapper {
  margin-top: 20px;
  display: flex;
  gap: 12px;
  padding: 0 20px 20px;
}

.card__btn {
  flex: 1;
  height: 36px;
  border: 2px solid var(--main-color);
  border-radius: 8px;
  font-weight: 600;
  font-size: 12px;
  color: var(--main-color);
  background: var(--bg-color);
  text-transform: uppercase;
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    background: var(--main-color);
    color: var(--bg-color);
  }
}

.card__btn-solid {
  background: var(--main-color);
  color: var(--bg-color);

  &:hover {
    background: var(--bg-color);
    color: var(--main-color);
  }
}

// 响应式布局
@media screen and (max-width: 1400px) {
  .templates-container {
    justify-content: flex-start;
  }
}

@media screen and (max-width: 768px) {
  .case-templates-page {
    padding: 16px;
  }

  .templates-container {
    gap: 16px;
    justify-content: center;
  }

  .card {
    width: 280px;
    height: 360px;

    .card__title {
      font-size: 16px;
    }

    .card__subtitle {
      font-size: 13px;
    }

    .card__btn {
      height: 32px;
      font-size: 11px;
    }
  }
}

@media screen and (max-width: 480px) {
  .card {
    width: 260px;
    height: 340px;
  }

  .card__wrapper {
    flex-direction: column;
    gap: 8px;
  }
}
</style>