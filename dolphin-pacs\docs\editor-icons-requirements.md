# 模板编辑器图标需求文档

## 概述
本文档列出了模板编辑器中需要的所有图标，用于替换当前使用的 emoji 和文本符号，提升用户界面的专业性和一致性。

## 图标分类及需求

### 1. 文件操作类图标
| 功能 | 当前显示 | 建议图标名称 | 描述 |
|------|----------|--------------|------|
| 保存 | 💾 保存 | `save` / `disk` | 保存文档图标 |
| 导出 | 📥 导出 | `download` / `export` | 下载/导出图标 |
| 导入 | 📤 导入 | `upload` / `import` | 上传/导入图标 |

### 2. 编辑操作类图标
| 功能 | 当前显示 | 建议图标名称 | 描述 |
|------|----------|--------------|------|
| 撤销 | ↶ | `undo` / `arrow-left-circle` | 撤销操作图标 |
| 重做 | ↷ | `redo` / `arrow-right-circle` | 重做操作图标 |
| 返回 | ArrowLeft (Element Plus) | `arrow-left` / `back` | 返回上一页图标 |

### 3. 文本格式类图标
| 功能 | 当前显示 | 建议图标名称 | 描述 |
|------|----------|--------------|------|
| 粗体 | **B** | `bold` / `type-bold` | 粗体文本图标 |
| 斜体 | *I* | `italic` / `type-italic` | 斜体文本图标 |
| 删除线 | ~~S~~ | `strikethrough` / `type-strikethrough` | 删除线图标 |
| 代码 | &lt;/&gt; | `code` / `terminal` | 代码格式图标 |

### 4. 文本对齐类图标
| 功能 | 当前显示 | 建议图标名称 | 描述 |
|------|----------|--------------|------|
| 左对齐 | ⬅️ | `align-left` / `text-left` | 左对齐图标 |
| 居中对齐 | ↔️ | `align-center` / `text-center` | 居中对齐图标 |
| 右对齐 | ➡️ | `align-right` / `text-right` | 右对齐图标 |
| 两端对齐 | ↕️ | `align-justify` / `text-justify` | 两端对齐图标 |

### 5. 列表和引用类图标
| 功能 | 当前显示 | 建议图标名称 | 描述 |
|------|----------|--------------|------|
| 无序列表 | • 列表 | `list-ul` / `list-unordered` | 无序列表图标 |
| 有序列表 | 1. 列表 | `list-ol` / `list-ordered` | 有序列表图标 |
| 引用 | " 引用 | `quote` / `blockquote` | 引用块图标 |

### 6. 表格操作类图标
| 功能 | 当前显示 | 建议图标名称 | 描述 |
|------|----------|--------------|------|
| 表格 | 📊 表格 ▼ | `table` / `grid` | 表格图标 |
| 插入表格 | - | `table-add` / `plus-square` | 插入表格图标 |
| 添加列 | - | `column-add` / `plus-circle` | 添加列图标 |
| 删除列 | - | `column-remove` / `minus-circle` | 删除列图标 |
| 添加行 | - | `row-add` / `plus-circle` | 添加行图标 |
| 删除行 | - | `row-remove` / `minus-circle` | 删除行图标 |
| 合并单元格 | - | `merge-cells` / `combine` | 合并单元格图标 |
| 拆分单元格 | - | `split-cells` / `separate` | 拆分单元格图标 |

### 7. 插入内容类图标
| 功能 | 当前显示 | 建议图标名称 | 描述 |
|------|----------|--------------|------|
| 插入链接 | 🔗 链接 | `link` / `chain` | 链接图标 |
| 插入图片 | 🖼️ 图片 | `image` / `picture` | 图片图标 |
| 分割线 | ➖ 分割线 | `minus` / `horizontal-rule` | 水平分割线图标 |

### 8. 页面操作类图标
| 功能 | 当前显示 | 建议图标名称 | 描述 |
|------|----------|--------------|------|
| 预览 | View (Element Plus) | `eye` / `preview` | 预览图标 |
| 保存模板 | Check (Element Plus) | `check` / `save` | 确认/保存图标 |
| 打印预览 | - | `printer` / `print` | 打印图标 |
| 警告 | Warning (Element Plus) | `warning` / `alert-triangle` | 警告图标 |

### 9. 状态指示类图标
| 功能 | 当前显示 | 建议图标名称 | 描述 |
|------|----------|--------------|------|
| 编辑模式 | 📝 编辑模式 | `edit` / `pencil` | 编辑图标 |
| 只读模式 | 👁️ 只读模式 | `eye` / `lock` | 只读/查看图标 |

## 推荐图标库

### 1. Heroicons (推荐)
- **优点**: 现代设计，与 Tailwind CSS 配套，图标齐全
- **网址**: https://heroicons.com/
- **格式**: SVG
- **许可**: MIT License

### 2. Lucide Icons (推荐)
- **优点**: 简洁美观，图标丰富，Vue 3 支持良好
- **网址**: https://lucide.dev/
- **格式**: SVG
- **许可**: ISC License

### 3. Tabler Icons
- **优点**: 专为 Web 应用设计，图标数量多
- **网址**: https://tabler-icons.io/
- **格式**: SVG
- **许可**: MIT License

### 4. Phosphor Icons
- **优点**: 多种风格，图标质量高
- **网址**: https://phosphoricons.com/
- **格式**: SVG
- **许可**: MIT License

## 实施建议

### 1. 图标规格
- **尺寸**: 16x16px, 20x20px, 24x24px
- **格式**: SVG (矢量格式，支持缩放)
- **颜色**: 单色，支持 CSS 颜色覆盖

### 2. 命名规范
- 使用语义化命名
- 采用 kebab-case 格式
- 例如: `text-bold`, `align-center`, `table-add`

### 3. 组织结构
```
src/assets/icons/
├── editor/           # 编辑器专用图标
│   ├── format/       # 格式化图标
│   ├── insert/       # 插入类图标
│   ├── table/        # 表格操作图标
│   └── file/         # 文件操作图标
└── common/           # 通用图标
```

### 4. 使用方式
建议创建图标组件，统一管理和使用：
```vue
<template>
  <Icon name="text-bold" :size="16" />
</template>
```

## 优先级

### 高优先级 (立即需要)
- 文本格式类图标 (粗体、斜体、删除线、代码)
- 文本对齐类图标
- 文件操作类图标 (保存、导出、导入)

### 中优先级 (近期需要)
- 编辑操作类图标 (撤销、重做)
- 列表和引用类图标
- 插入内容类图标

### 低优先级 (后续优化)
- 表格操作类图标 (可以先使用下拉菜单文字)
- 状态指示类图标

## 注意事项

1. **一致性**: 选择同一图标库，保持视觉风格统一
2. **可访问性**: 确保图标有适当的 aria-label 或 title 属性
3. **响应式**: 图标在不同屏幕尺寸下都应清晰可见
4. **性能**: 考虑图标的加载性能，建议使用 SVG sprite 或图标字体
5. **主题适配**: 图标应支持深色/浅色主题切换

## 总结

总计需要约 **30+ 个图标**，建议选择 Heroicons 或 Lucide Icons 作为主要图标库，优先实现高优先级图标，逐步替换现有的 emoji 和文本符号，提升编辑器的专业性和用户体验。
