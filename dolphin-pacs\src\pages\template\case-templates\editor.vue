<script lang="ts" setup>
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { ArrowLeft, View, Check, Warning } from "@element-plus/icons-vue";
import TemplateEditor from "./components/TemplateEditor.vue";

defineOptions({
  name: "TemplateEditorPage",
});

const route = useRoute();
const router = useRouter();

// 模板信息
const templateId = computed(() => route.params.id as string);
const templateName = computed(() => route.query.name as string);

// 编辑器状态
const templateContent = ref("");
const previewVisible = ref(false);
const hasUnsavedChanges = ref(false);

// 返回确认对话框状态
const backConfirmVisible = ref(false);

// 计算编辑器高度
const editorHeight = computed(() => {
  return window.innerHeight - 160; // 减去头部和其他元素的高度
});

// 返回上一页
const goBack = () => {
  if (hasUnsavedChanges.value) {
    backConfirmVisible.value = true;
  } else {
    router.back();
  }
};

// 确认返回
const confirmBack = () => {
  backConfirmVisible.value = false;
  router.back();
};

// 保存模板
const handleSave = async () => {
  try {
    // 这里应该调用API保存模板
    console.log("保存模板:", {
      id: templateId.value,
      name: templateName.value,
      content: templateContent.value,
    });

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));

    hasUnsavedChanges.value = false;
    ElMessage.success("模板保存成功");
  } catch (error) {
    ElMessage.error("保存失败，请重试");
    console.error("保存模板失败:", error);
  }
};

// 预览模板
const handlePreview = () => {
  if (!templateContent.value.trim()) {
    ElMessage.warning("模板内容为空，无法预览");
    return;
  }
  previewVisible.value = true;
};

// 关闭预览
const handleClosePreview = () => {
  previewVisible.value = false;
};

// 打印预览
const handlePrint = () => {
  const printWindow = window.open("", "_blank");
  if (printWindow) {
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>模板预览 - ${templateName.value}</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              line-height: 1.6; 
              margin: 20px; 
            }
            @media print {
              body { margin: 0; }
            }
          </style>
        </head>
        <body>
          ${templateContent.value}
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  }
};

// 内容变化处理
const handleContentChange = (content: string) => {
  hasUnsavedChanges.value = true;
};

// 加载模板数据
const loadTemplate = async () => {
  if (templateId.value && templateId.value !== "new") {
    try {
      // 这里应该调用API加载模板数据
      console.log("加载模板:", templateId.value);

      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 500));

      // 模拟加载的模板内容
      templateContent.value = `
        <div style="font-family: SimSun, serif; font-size: 14px; line-height: 1.6; padding: 20px; max-width: 600px; margin: 0 auto;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="margin: 0; font-size: 16px; font-weight: bold; text-align: center;">XXX 医院心脏超声检查报告单（简版）</h2>
          </div>

          <div style="margin-bottom: 15px; line-height: 2;">
            姓名<span style="border-bottom: 1px solid #000; display: inline-block; width: 80px; margin: 0 20px 0 5px;">___________</span>
            性别<span style="border-bottom: 1px solid #000; display: inline-block; width: 60px; margin: 0 20px 0 5px;">___________</span>
            年龄<span style="border-bottom: 1px solid #000; display: inline-block; width: 60px; margin: 0 20px 0 5px;">___________</span>
            病历号<span style="border-bottom: 1px solid #000; display: inline-block; width: 80px; margin: 0 0 0 5px;">___________</span>
          </div>

          <div style="margin-bottom: 15px; line-height: 2;">
            病案号（门诊号/住院号）<span style="border-bottom: 1px solid #000; display: inline-block; width: 120px; margin: 0 0 0 5px;">_______________</span>
          </div>

          <div style="margin-bottom: 15px; line-height: 2;">
            病室<span style="border-bottom: 1px solid #000; display: inline-block; width: 80px; margin: 0 20px 0 5px;">___________</span>
            床号<span style="border-bottom: 1px solid #000; display: inline-block; width: 60px; margin: 0 20px 0 5px;">___________</span>
            收费<span style="border-bottom: 1px solid #000; display: inline-block; width: 80px; margin: 0 20px 0 5px;">___________</span>
            检查机器号<span style="border-bottom: 1px solid #000; display: inline-block; width: 100px; margin: 0 0 0 5px;">_______________</span>
          </div>

          <div style="margin-bottom: 15px; line-height: 2;">
            检查时间：<span style="border-bottom: 1px solid #000; display: inline-block; width: 120px; margin: 0 20px 0 5px;">_______________</span>
            申请科室<span style="border-bottom: 1px solid #000; display: inline-block; width: 100px; margin: 0 0 0 5px;">_______________</span>
          </div>

          <div style="margin-bottom: 15px; line-height: 2;">
            <span style="display: inline-block; width: 60px; vertical-align: bottom;">临床：</span>
            <span style="display: inline-block; width: 40px; margin-left: 20px; vertical-align: bottom;">甲</span>
            <span style="display: inline-block; width: 40px; margin-left: 20px; vertical-align: bottom;">乙</span>
            <span style="display: inline-block; width: 40px; margin-left: 20px; vertical-align: bottom;">丙</span>
            <span style="display: inline-block; width: 40px; margin-left: 20px; vertical-align: bottom;">丁</span>
            <span style="display: inline-block; width: 80px; margin-left: 40px; vertical-align: bottom;">图像质量：</span>
            <span style="display: inline-block; width: 20px; margin-left: 10px; vertical-align: bottom;">A</span>
            <span style="display: inline-block; width: 20px; margin-left: 10px; vertical-align: bottom;">B</span>
            <span style="display: inline-block; width: 20px; margin-left: 10px; vertical-align: bottom;">C</span>
            <span style="display: inline-block; width: 20px; margin-left: 10px; vertical-align: bottom;">D</span>
          </div>

          <div style="display: flex; margin-bottom: 20px;">
            <div style="border: 1px solid #000; width: 200px; height: 150px; margin-right: 20px; text-align: center; line-height: 150px;">图片 1</div>
            <div style="border: 1px solid #000; width: 200px; height: 150px; text-align: center; line-height: 150px;">图片 2</div>
          </div>

          <div style="margin-bottom: 20px;">
            <h3 style="font-size: 12px; margin: 10px 0; font-weight: bold;">M型超声心动图/二维超声心动图 （单位：cm）</h3>
            <table style="width: 100%; border-collapse: collapse; font-size: 11px;">
              <tr>
                <td style="border: 1px solid #000; padding: 4px; width: 15%;">主动脉径</td>
                <td style="border: 1px solid #000; padding: 4px; width: 15%;">室间隔厚度</td>
                <td style="border: 1px solid #000; padding: 4px; width: 15%;">左室舒张末径</td>
                <td style="border: 1px solid #000; padding: 4px; width: 15%;">左室</td>
                <td style="border: 1px solid #000; padding: 4px; width: 20%;"></td>
                <td style="border: 1px solid #000; padding: 4px; width: 20%;"></td>
              </tr>
              <tr>
                <td style="border: 1px solid #000; padding: 4px;">AO</td>
                <td style="border: 1px solid #000; padding: 4px;">IVSd</td>
                <td style="border: 1px solid #000; padding: 4px;">LVDd</td>
                <td style="border: 1px solid #000; padding: 4px;">EF (%)</td>
                <td style="border: 1px solid #000; padding: 4px;"></td>
                <td style="border: 1px solid #000; padding: 4px;"></td>
              </tr>
              <tr>
                <td style="border: 1px solid #000; padding: 4px;">左房前后径</td>
                <td style="border: 1px solid #000; padding: 4px;">左室后壁厚度</td>
                <td style="border: 1px solid #000; padding: 4px;">左室收缩末径</td>
                <td style="border: 1px solid #000; padding: 4px;">FS (%)</td>
                <td style="border: 1px solid #000; padding: 4px;"></td>
                <td style="border: 1px solid #000; padding: 4px;"></td>
              </tr>
              <tr>
                <td style="border: 1px solid #000; padding: 4px;">LA</td>
                <td style="border: 1px solid #000; padding: 4px;">LVPWd</td>
                <td style="border: 1px solid #000; padding: 4px;">LVDs</td>
                <td style="border: 1px solid #000; padding: 4px;">SV (ml)</td>
                <td style="border: 1px solid #000; padding: 4px;"></td>
                <td style="border: 1px solid #000; padding: 4px;"></td>
              </tr>
              <tr>
                <td style="border: 1px solid #000; padding: 4px;">右室 RV</td>
                <td style="border: 1px solid #000; padding: 4px;">HR</td>
                <td style="border: 1px solid #000; padding: 4px;"></td>
                <td style="border: 1px solid #000; padding: 4px;"></td>
                <td style="border: 1px solid #000; padding: 4px;"></td>
                <td style="border: 1px solid #000; padding: 4px;"></td>
              </tr>
            </table>
          </div>

          <div style="margin-bottom: 20px;">
            <div style="font-size: 12px; margin: 10px 0; font-weight: bold;">
              超声检查所见：<span style="border-bottom: 1px solid #000; display: inline-block; width: calc(100% - 120px); margin-left: 10px;"></span>
            </div>
            <div style="margin: 10px 0;">
              <span style="border-bottom: 1px solid #000; display: inline-block; width: 100%; margin: 5px 0;"></span>
            </div>
            <div style="margin: 10px 0;">
              <span style="border-bottom: 1px solid #000; display: inline-block; width: 100%; margin: 5px 0;"></span>
            </div>
            <div style="margin: 10px 0;">
              <span style="border-bottom: 1px solid #000; display: inline-block; width: 100%; margin: 5px 0;"></span>
            </div>
            <div style="margin: 10px 0;">
              <span style="border-bottom: 1px solid #000; display: inline-block; width: 100%; margin: 5px 0;"></span>
            </div>
          </div>

          <div style="margin-bottom: 20px;">
            <div style="font-size: 12px; margin: 10px 0; font-weight: bold;">
              超声检查提示/超声诊断：<span style="border-bottom: 1px solid #000; display: inline-block; width: calc(100% - 180px); margin-left: 10px;"></span>
            </div>
            <div style="margin: 10px 0;">
              <span style="border-bottom: 1px solid #000; display: inline-block; width: 100%; margin: 5px 0;"></span>
            </div>
            <div style="margin: 10px 0;">
              <span style="border-bottom: 1px solid #000; display: inline-block; width: 100%; margin: 5px 0;"></span>
            </div>
            <div style="margin: 10px 0;">
              <span style="border-bottom: 1px solid #000; display: inline-block; width: 100%; margin: 5px 0;"></span>
            </div>
          </div>

          <div style="margin-top: 30px;">
            <div style="border-top: 1px solid #000; padding-top: 15px;">
              <div style="display: flex; justify-content: space-between; margin-bottom: 15px; font-size: 12px;">
                <div>报告时间：<span style="border-bottom: 1px solid #000; display: inline-block; width: 120px; margin-left: 5px;"></span></div>
                <div>会诊医师：<span style="border-bottom: 1px solid #000; display: inline-block; width: 100px; margin-left: 5px;"></span></div>
                <div>医师助理：<span style="border-bottom: 1px solid #000; display: inline-block; width: 100px; margin-left: 5px;"></span></div>
                <div>检查医师：<span style="border-bottom: 1px solid #000; display: inline-block; width: 100px; margin-left: 5px;"></span></div>
                <div>签名：<span style="border-bottom: 1px solid #000; display: inline-block; width: 80px; margin-left: 5px;"></span></div>
              </div>
            </div>
          </div>

          <div style="text-align: center; margin-top: 20px; font-size: 10px;">
            本报告仅供临床医师参考
          </div>

          <div style="text-align: right; margin-top: 10px; font-size: 10px;">
            第 <span style="border-bottom: 1px solid #000; display: inline-block; width: 30px; margin: 0 5px;"></span> 页 共 <span style="border-bottom: 1px solid #000; display: inline-block; width: 30px; margin: 0 5px;"></span> 页
          </div>
        </div>
      `;
    } catch (error) {
      ElMessage.error("加载模板失败");
      console.error("加载模板失败:", error);
    }
  }
};

// 页面离开前确认
const beforeUnload = (e: BeforeUnloadEvent) => {
  if (hasUnsavedChanges.value) {
    e.preventDefault();
    e.returnValue = "";
  }
};

onMounted(() => {
  loadTemplate();
  window.addEventListener("beforeunload", beforeUnload);
});

onBeforeUnmount(() => {
  window.removeEventListener("beforeunload", beforeUnload);
});
</script>

<template>
  <div class="template-editor-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" :icon="ArrowLeft" size="default">
          返回
        </el-button>
        <div class="template-info">
          <h1 class="template-title">{{ templateName || "新建模板" }}</h1>

        </div>
      </div>
      <div class="header-right">
        <el-button @click="handlePreview" :icon="View" size="default">
          预览
        </el-button>
        <el-button
          @click="handleSave"
          type="primary"
          :icon="Check"
          size="default"
        >
          保存模板
        </el-button>
      </div>
    </div>

    <!-- 编辑器区域 -->
    <div class="editor-container">
      <TemplateEditor
        v-model="templateContent"
        :height="editorHeight"
        :editable="true"
        placeholder="请开始编辑您的病例报告模板..."
        @save="handleSave"
        @change="handleContentChange"
      />
    </div>

    <!-- 预览弹窗 -->
    <el-dialog
      v-model="previewVisible"
      title="模板预览"
      width="80%"
      :before-close="handleClosePreview"
      class="preview-dialog"
    >
      <div class="preview-content" v-html="templateContent"></div>
      <template #footer>
        <el-button @click="handleClosePreview">关闭</el-button>
        <el-button type="primary" @click="handlePrint">打印预览</el-button>
      </template>
    </el-dialog>

    <!-- 返回确认弹窗 -->
    <el-dialog
      v-model="backConfirmVisible"
      title="返回确认"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="confirm-content">
        <el-icon class="confirm-icon" color="#f56c6c" :size="24">
          <Warning />
        </el-icon>
        <div class="confirm-text">
          <p>您有未保存的更改，确定要离开吗？</p>
          <p class="warning-text">离开后未保存的更改将丢失！</p>
        </div>
      </div>

      <template #footer>
        <el-button @click="backConfirmVisible = false">取消</el-button>
        <el-button
          type="danger"
          @click="confirmBack"
        >
          确定离开
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>


<style lang="scss" scoped>
.template-editor-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color-page);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: white;
  border-bottom: 1px solid var(--el-border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .template-info {
      .template-title {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }

      .template-description {
        margin: 2px 0 0 0;
        font-size: 13px;
        color: var(--el-text-color-secondary);
      }
    }
  }

  .header-right {
    display: flex;
    gap: 12px;
  }
}

.editor-container {
  flex: 1;
  padding: 24px;
  overflow: hidden;
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
}

:deep(.preview-dialog) {
  .el-dialog {
    max-height: 80vh;
  }

  .el-dialog__body {
    max-height: 60vh;
    overflow-y: auto;
  }
}

.preview-content {
  padding: 20px;
  background: white;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;

  :deep(h1) {
    text-align: center;
    margin-bottom: 24px;
    color: var(--el-text-color-primary);
  }

  :deep(h2) {
    margin: 20px 0 12px 0;
    color: var(--el-text-color-primary);
    border-bottom: 1px solid var(--el-border-color);
    padding-bottom: 8px;
  }

  :deep(p) {
    margin: 8px 0;
    line-height: 1.6;
  }

  :deep(table) {
    width: 100%;
    border-collapse: collapse;
    margin: 16px 0;

    th,
    td {
      border: 1px solid var(--el-border-color);
      padding: 8px 12px;
      text-align: left;
    }

    th {
      background: var(--el-bg-color-page);
      font-weight: 600;
    }
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    padding: 16px;

    .header-left {
      width: 100%;
      justify-content: flex-start;
    }

    .header-right {
      width: 100%;
      justify-content: flex-end;
    }

    .template-info {
      .template-title {
        font-size: 20px;
      }
    }
  }

  .editor-container {
    padding: 16px;
  }
}

// 确认对话框样式
.confirm-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;

  .confirm-icon {
    flex-shrink: 0;
    margin-top: 2px;
  }

  .confirm-text {
    flex: 1;

    p {
      margin: 0 0 8px 0;
      line-height: 1.5;
    }

    .warning-text {
      color: #f56c6c;
      font-weight: bold;
      margin-top: 12px !important;
    }
  }
}
</style>
